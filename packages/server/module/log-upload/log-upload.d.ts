declare namespace Module.LogUpload {
  export interface UploadState {
    filePath: string;
    lastUploadPosition: number;
    lastUploadTime: number;
    fileSize: number;
  }

  export interface UploadStatus {
    [filePath: string]: UploadState;
  }

  export interface UploadResult {
    success: number;
    failed: number;
    total: number;
  }

  export interface UploadStats {
    totalFiles: number;
    totalUploadedBytes: number;
    lastUploadTime: number | null;
    states: UploadStatus;
  }

  export interface ScheduledTaskInfo {
    active: boolean;
    nextInvocation: string | null;
  }

  export interface StatusResponse {
    code: number;
    message: string;
    data?: {
      totalFiles: number;
      totalUploadedBytes: number;
      lastUploadTime: number | null;
      states: UploadStatus;
      scheduledTasks: {
        uploadJob: ScheduledTaskInfo;
        cleanupJob: ScheduledTaskInfo;
      };
    };
    error?: string;
  }

  export interface TriggerResponse {
    code: number;
    message: string;
    data?: UploadResult;
    error?: string;
  }

  export interface IncrementalContent {
    content: string;
    newPosition: number;
  }
}
