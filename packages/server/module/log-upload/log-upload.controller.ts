import { Controller, Post, Get, Inject } from '@nestjs/common';
import * as schedule from 'node-schedule';
import LogUploadService, { LOG_UPLOAD_CONFIG } from './log-upload.service';

@Controller('api/log-upload')
export default class LogUploadController {
  @Inject(LogUploadService)
  private logUploadService: LogUploadService;

  private uploadJob: schedule.Job | null = null;
  private cleanupJob: schedule.Job | null = null;

  constructor() {
    this.initScheduledTasks();
  }

  /**
   * 初始化定时任务
   */
  private initScheduledTasks(): void {
    try {
      // 根据配置的间隔执行日志上传（默认30分钟）
      const uploadCron = `*/${LOG_UPLOAD_CONFIG.UPLOAD_INTERVAL_MINUTES} * * * *`;
      this.uploadJob = schedule.scheduleJob(uploadCron, async () => {
        console.log('开始执行定时日志上传任务');
        try {
          const result = await this.logUploadService.uploadAllLogIncrements();
          console.log(`定时日志上传任务完成: ${JSON.stringify(result)}`);
        } catch (error) {
          console.error('定时日志上传任务失败:', error);
        }
      });

      // 每天凌晨2点清理过期状态
      this.cleanupJob = schedule.scheduleJob(LOG_UPLOAD_CONFIG.CLEANUP_CRON, () => {
        console.log('开始执行日志状态清理任务');
        try {
          this.logUploadService.cleanupOldStates();
          console.log('日志状态清理任务完成');
        } catch (error) {
          console.error('日志状态清理任务失败:', error);
        }
      });

      console.log(`日志上传定时任务初始化完成 - 上传间隔: ${LOG_UPLOAD_CONFIG.UPLOAD_INTERVAL_MINUTES}分钟`);
    } catch (error) {
      console.error('日志上传定时任务初始化失败:', error);
    }
  }



  /**
   * 获取上传状态
   */
  @Get('/status')
  async getUploadStatus() {
    try {
      const stats = this.logUploadService.getUploadStats();

      return {
        code: 1,
        message: '获取状态成功',
        data: {
          ...stats,
          scheduledTasks: {
            uploadJob: {
              active: !!this.uploadJob,
              nextInvocation: this.uploadJob?.nextInvocation()?.toISOString() || null
            },
            cleanupJob: {
              active: !!this.cleanupJob,
              nextInvocation: this.cleanupJob?.nextInvocation()?.toISOString() || null
            }
          }
        }
      };
    } catch (error) {
      console.error('获取上传状态失败:', error);
      return {
        code: -1,
        message: '获取状态失败',
        error: error.message
      };
    }
  }

  /**
   * 清理过期状态
   */
  @Post('/cleanup')
  async cleanupStates() {
    try {
      console.log('手动触发状态清理');
      this.logUploadService.cleanupOldStates();

      return {
        code: 1,
        message: '状态清理完成'
      };
    } catch (error) {
      console.error('手动触发状态清理失败:', error);
      return {
        code: -1,
        message: '状态清理失败',
        error: error.message
      };
    }
  }

  /**
   * 停止定时任务
   */
  @Post('/stop')
  async stopScheduledTasks() {
    try {
      if (this.uploadJob) {
        this.uploadJob.cancel();
        this.uploadJob = null;
      }

      if (this.cleanupJob) {
        this.cleanupJob.cancel();
        this.cleanupJob = null;
      }

      console.log('定时任务已停止');
      return {
        code: 1,
        message: '定时任务已停止'
      };
    } catch (error) {
      console.error('停止定时任务失败:', error);
      return {
        code: -1,
        message: '停止定时任务失败',
        error: error.message
      };
    }
  }

  /**
   * 重启定时任务
   */
  @Post('/restart')
  async restartScheduledTasks() {
    try {
      // 先停止现有任务
      await this.stopScheduledTasks();

      // 重新初始化
      this.initScheduledTasks();

      console.log('定时任务已重启');
      return {
        code: 1,
        message: '定时任务已重启'
      };
    } catch (error) {
      console.error('重启定时任务失败:', error);
      return {
        code: -1,
        message: '重启定时任务失败',
        error: error.message
      };
    }
  }

  /**
   * 组件销毁时清理定时任务
   */
  onModuleDestroy() {
    if (this.uploadJob) {
      this.uploadJob.cancel();
    }
    if (this.cleanupJob) {
      this.cleanupJob.cancel();
    }
    console.log('日志上传定时任务已清理');
  }
}
