# 日志上传服务 (Log Upload Service)

## 功能概述

日志上传服务提供了将请求日志实时上传到CDN的功能，支持增量上传，避免重复上传已处理的日志内容。

## 主要特性

- **增量上传**: 只上传新增的日志内容，避免重复上传
- **定时任务**: 默认每30分钟自动执行一次日志上传（可配置）
- **状态管理**: 跟踪每个日志文件的上传进度
- **错误处理**: 上传失败时记录错误，不影响后续上传
- **状态清理**: 每天自动清理过期的上传状态记录
- **可配置**: 上传频率等参数可通过常量配置

## 配置参数

在 `log-upload.service.ts` 中的 `LOG_UPLOAD_CONFIG` 常量可以调整以下参数：

```typescript
export const LOG_UPLOAD_CONFIG = {
  // 上传频率（分钟）- 可以手动调整
  UPLOAD_INTERVAL_MINUTES: 30,
  // 清理频率（每天凌晨2点）
  CLEANUP_CRON: '0 2 * * *',
  // 状态保留天数
  STATE_RETENTION_DAYS: 7,
  // CDN上传路径
  CDN_UPLOAD_PATH: '/request-logs'
};
```

## API 接口

### 1. 获取上传状态
```
GET /api/log-upload/status
```

**响应示例:**
```json
{
  "code": 1,
  "message": "获取状态成功",
  "data": {
    "totalFiles": 5,
    "totalUploadedBytes": 1024000,
    "lastUploadTime": 1640995200000,
    "states": {
      "/path/to/log/2024-01-01.jsonl": {
        "filePath": "/path/to/log/2024-01-01.jsonl",
        "lastUploadPosition": 1024,
        "lastUploadTime": 1640995200000,
        "fileSize": 1024
      }
    },
    "scheduledTasks": {
      "uploadJob": {
        "active": true,
        "nextInvocation": "2024-01-01T10:10:00.000Z"
      },
      "cleanupJob": {
        "active": true,
        "nextInvocation": "2024-01-02T02:00:00.000Z"
      }
    }
  }
}
```

### 2. 清理过期状态
```
POST /api/log-upload/cleanup
```

### 3. 停止定时任务
```
POST /api/log-upload/stop
```

### 4. 重启定时任务
```
POST /api/log-upload/restart
```

## 定时任务

### 上传任务
- **频率**: 默认每30分钟执行一次（可通过 `LOG_UPLOAD_CONFIG.UPLOAD_INTERVAL_MINUTES` 配置）
- **Cron表达式**: `*/30 * * * *`（动态生成）
- **功能**: 扫描日志目录，上传所有日志文件的增量内容

### 清理任务
- **频率**: 每天凌晨2点执行
- **Cron表达式**: `0 2 * * *`
- **功能**: 清理超过7天的上传状态记录

## 文件结构

```
logs/request-logs/
├── 2024-01-01.jsonl          # 日志文件
├── 2024-01-02.jsonl          # 日志文件
├── .upload-state.json        # 上传状态文件
```

## 上传状态文件格式

`.upload-state.json` 文件记录每个日志文件的上传状态：

```json
{
  "/path/to/log/2024-01-01.jsonl": {
    "filePath": "/path/to/log/2024-01-01.jsonl",
    "lastUploadPosition": 1024,
    "lastUploadTime": 1640995200000,
    "fileSize": 1024
  }
}
```

## CDN上传路径

上传到CDN的文件路径格式：
```
/request-logs/{filename}_increment_{timestamp}.jsonl
```

示例：
```
/request-logs/2024-01-01_increment_2024-01-01_10-30-00.jsonl
```

## 错误处理

- 上传失败时会记录错误日志，但不会中断其他文件的上传
- 网络错误或CDN服务异常时会自动重试（下次定时任务执行时）
- 文件读取错误时会跳过该文件，继续处理其他文件

## 监控建议

1. 定期检查 `/api/log-upload/status` 接口，监控上传状态
2. 关注日志中的错误信息，及时处理上传失败的情况
3. 监控CDN存储空间使用情况
4. 定期检查定时任务是否正常运行

## 注意事项

1. 确保CDN服务配置正确且可用
2. 日志目录需要有足够的读写权限
3. 服务重启后定时任务会自动恢复
4. 上传状态文件损坏时会重新开始上传（可能导致重复上传）
