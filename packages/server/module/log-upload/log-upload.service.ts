import { Injectable, Inject } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import dayjs from 'dayjs';
import CDNService from '../cdn/cdn.service';

// 配置常量
export const LOG_UPLOAD_CONFIG = {
  // 上传频率（分钟）- 可以手动调整
  UPLOAD_INTERVAL_MINUTES: 30,
  // 清理频率（每天凌晨2点）
  CLEANUP_CRON: '0 2 * * *',
  // 状态保留天数
  STATE_RETENTION_DAYS: 7,
  // CDN上传路径
  CDN_UPLOAD_PATH: '/request-logs'
};

@Injectable()
export default class LogUploadService {
  @Inject(CDNService)
  private cdnService: CDNService;

  private readonly logFolder: string;
  private readonly stateFile: string;
  private readonly uploadStates: Module.LogUpload.UploadStatus = {};

  constructor() {
    this.logFolder = path.join(process.cwd(), '../../logs/request-logs');
    this.stateFile = path.join(this.logFolder, '.upload-state.json');
    this.loadUploadStates();
  }

  /**
   * 加载上传状态
   */
  private loadUploadStates(): void {
    try {
      if (fs.existsSync(this.stateFile)) {
        const stateData = fs.readFileSync(this.stateFile, 'utf-8');
        Object.assign(this.uploadStates, JSON.parse(stateData));
      }
    } catch (error) {
      console.error('加载日志上传状态失败:', error);
    }
  }

  /**
   * 保存上传状态
   */
  private saveUploadStates(): void {
    try {
      fs.writeFileSync(this.stateFile, JSON.stringify(this.uploadStates, null, 2));
    } catch (error) {
      console.error('保存日志上传状态失败:', error);
    }
  }

  /**
   * 获取日志文件的增量内容
   */
  private getIncrementalContent(filePath: string): Module.LogUpload.IncrementalContent | null {
    try {
      if (!fs.existsSync(filePath)) {
        return null;
      }

      const stats = fs.statSync(filePath);
      const currentSize = stats.size;

      const state = this.uploadStates[filePath];
      const lastPosition = state?.lastUploadPosition || 0;

      // 如果文件没有新内容
      if (currentSize <= lastPosition) {
        return null;
      }

      // 读取增量内容
      const fd = fs.openSync(filePath, 'r');
      const bufferSize = currentSize - lastPosition;
      const buffer = Buffer.alloc(bufferSize);

      fs.readSync(fd, buffer, 0, bufferSize, lastPosition);
      fs.closeSync(fd);

      const content = buffer.toString('utf-8');

      return {
        content,
        newPosition: currentSize
      };
    } catch (error) {
      console.error(`读取日志文件增量内容失败: ${filePath}`, error);
      return null;
    }
  }

  /**
   * 上传单个日志文件的增量内容
   */
  private async uploadLogFileIncrement(filePath: string): Promise<boolean> {
    try {
      const incrementalData = this.getIncrementalContent(filePath);

      if (!incrementalData || !incrementalData.content.trim()) {
        return true; // 没有新内容，视为成功
      }

      const fileName = path.basename(filePath);
      const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
      const uploadFileName = `${fileName.replace('.jsonl', '')}_increment_${timestamp}.jsonl`;

      // 上传到CDN
      const cdnUrl = await this.cdnService.uploadToCDN({
        str: incrementalData.content,
        filename: uploadFileName,
        path: LOG_UPLOAD_CONFIG.CDN_UPLOAD_PATH
      });

      // 更新上传状态
      this.uploadStates[filePath] = {
        filePath,
        lastUploadPosition: incrementalData.newPosition,
        lastUploadTime: Date.now(),
        fileSize: incrementalData.newPosition
      };

      console.log(`日志增量上传成功: ${filePath} -> ${cdnUrl}`);
      return true;
    } catch (error) {
      console.error(`日志增量上传失败: ${filePath}`, error);
      return false;
    }
  }

  /**
   * 扫描并上传所有日志文件的增量内容
   */
  async uploadAllLogIncrements(): Promise<Module.LogUpload.UploadResult> {
    const result = { success: 0, failed: 0, total: 0 };

    try {
      if (!fs.existsSync(this.logFolder)) {
        console.warn(`日志目录不存在: ${this.logFolder}`);
        return result;
      }

      const files = fs.readdirSync(this.logFolder);
      const logFiles = files.filter(file =>
        file.endsWith('.jsonl') &&
        !file.startsWith('.') &&
        file !== '.upload-state.json'
      );

      result.total = logFiles.length;

      for (const file of logFiles) {
        const filePath = path.join(this.logFolder, file);
        const success = await this.uploadLogFileIncrement(filePath);

        if (success) {
          result.success++;
        } else {
          result.failed++;
        }
      }

      // 保存状态
      this.saveUploadStates();

      console.log(`日志增量上传完成: 成功 ${result.success}, 失败 ${result.failed}, 总计 ${result.total}`);
    } catch (error) {
      console.error('扫描日志文件失败:', error);
    }

    return result;
  }

  /**
   * 清理过期的上传状态（超过7天的文件状态）
   */
  cleanupOldStates(): void {
    try {
      const retentionTime = Date.now() - LOG_UPLOAD_CONFIG.STATE_RETENTION_DAYS * 24 * 60 * 60 * 1000;

      Object.keys(this.uploadStates).forEach(filePath => {
        const state = this.uploadStates[filePath];

        // 如果文件不存在或状态过期，删除状态记录
        if (!fs.existsSync(filePath) || state.lastUploadTime < retentionTime) {
          delete this.uploadStates[filePath];
        }
      });

      this.saveUploadStates();
      console.log('清理过期日志上传状态完成');
    } catch (error) {
      console.error('清理过期日志上传状态失败:', error);
    }
  }

  /**
   * 获取上传状态统计
   */
  getUploadStats(): Module.LogUpload.UploadStats {
    const states = { ...this.uploadStates };
    const totalFiles = Object.keys(states).length;
    const totalUploadedBytes = Object.values(states).reduce((sum, state) => sum + state.fileSize, 0);
    const lastUploadTime = Object.values(states).reduce((latest, state) =>
      Math.max(latest, state.lastUploadTime), 0) || null;

    return {
      totalFiles,
      totalUploadedBytes,
      lastUploadTime,
      states
    };
  }
}
